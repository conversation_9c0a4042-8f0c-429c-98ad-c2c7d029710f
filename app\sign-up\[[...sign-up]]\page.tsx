'use client';

import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
// Image component might not be needed if logo is text based
// import Image from 'next/image';
import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FaEye, FaEyeSlash, FaGithub, FaGoogle } from 'react-icons/fa';
import { FaLinkedinIn, FaXTwitter } from 'react-icons/fa6';
import { toast } from 'react-toastify';
import { z } from 'zod';

// Updated schema for sign-up, matching the UI fields
const signUpSchema = z.object({
  identifier: z.string().min(1, 'Email or Phone number is required'), // Can be email or phone
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  pin: z
    .string()
    .min(6, 'PIN must be 6 digits')
    .max(6, 'PIN must be 6 digits')
    .regex(/^\d{6}$/, 'PIN must be 6 numeric digits'),
});

type SignUpFormData = z.infer<typeof signUpSchema>;

export default function SignUpPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'login' | 'register'>('register'); // Default to 'register'
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
  });

  const onSubmit: SubmitHandler<SignUpFormData> = async data => {
    if (activeTab === 'login') {
      // Handle login logic here or redirect to sign-in page
      toast.info('Please go to the login page to sign in.');
      router.push('/sign-in');
      return;
    }

    setIsLoading(true);
    try {
      // Create user account
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: data.fullName,
          // Assuming your backend expects 'email' for identifier
          // and 'password' for pin. Adjust if your API endpoint differs.
          email: data.identifier,
          password: data.pin,
        }),
      });

      if (response.ok) {
        toast.success('Account created successfully! Signing you in...');
        // Sign in the user after successful registration
        const result = await signIn('credentials', {
          email: data.identifier,
          password: data.pin,
          redirect: false,
        });

        if (result?.error) {
          toast.error('Account created but auto sign-in failed. Please try signing in manually.');
          router.push('/sign-in'); // Redirect to sign-in if auto login fails
        } else {
          router.push('/dashboard'); // Redirect to dashboard on successful sign-in
        }
      } else {
        const errorData = await response.json();
        toast.error(
          errorData.message ||
            'Failed to create account. The email or phone number might already be in use.',
        );
      }
    } catch (error) {
      console.error('Sign up error:', error);
      toast.error('An error occurred during sign up. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignIn = async (provider: 'facebook' | 'google') => {
    setIsLoading(true);
    try {
      // Social sign-in can also be used for sign-up if the user doesn't exist
      await signIn(provider, { callbackUrl: '/dashboard' });
    } catch (error) {
      toast.error(`An error occurred with ${provider} sign in/up.`);
    }
    // setIsLoading(false); // NextAuth handles redirection or error display
  };

  return (
    <div className="min-h-screen bg-[#171717] flex font-sans">
      {' '}
      {/* Main background color */}
      {/* Left Branding Panel */}
      <div className="hidden lg:flex flex-col w-1/2 bg-[#CC552E] bg-dot-pattern-orange items-center justify-center p-12 relative">
        {/* Logo */}
        <div className="flex items-center">
          <span className="text-7xl font-bold text-white tracking-tight">typing</span>
          <div className="ml-1 -mt-1 flex items-center justify-center w-10 h-10 bg-black rounded-full">
            {' '}
            {/* Black circle for .co */}
            <span className="text-white text-sm font-semibold">.co</span>
          </div>
        </div>

        {/* Social Icons at the bottom */}
        <div className="absolute bottom-10 left-10 flex space-x-4">
          <Link href="#" aria-label="X (Twitter)" className="text-gray-200 hover:text-white p-1">
            <FaXTwitter size={20} />
          </Link>
          <span className="text-gray-300 text-xl leading-none select-none" aria-hidden="true">
            •
          </span>
          <Link href="#" aria-label="LinkedIn" className="text-gray-200 hover:text-white p-1">
            <FaLinkedinIn size={20} />
          </Link>
        </div>
      </div>
      {/* Right Form Panel */}
      <div className="w-full lg:w-1/2 bg-[#212121] flex flex-col items-center justify-center p-6 sm:p-10 relative">
        <div className="w-full max-w-sm">
          {/* Tabs */}
          <div className="flex mb-8">
            <button
              onClick={() => setActiveTab('login')}
              className={`flex-1 py-3 text-sm font-semibold rounded-lg transition-all duration-300 ease-in-out
                ${
                  activeTab === 'login'
                    ? 'bg-gradient-to-r from-red-500 to-orange-500 text-white shadow-md'
                    : 'bg-[#3A3A3A] text-gray-400 hover:bg-[#4A4A4A]'
                }`}
            >
              Login
            </button>
            <button
              onClick={() => setActiveTab('register')}
              className={`flex-1 py-3 text-sm font-semibold rounded-lg ml-2 transition-all duration-300 ease-in-out
                ${
                  activeTab === 'register'
                    ? 'bg-gradient-to-r from-red-500 to-orange-500 text-white shadow-md'
                    : 'bg-[#3A3A3A] text-gray-400 hover:bg-[#4A4A4A]'
                }`}
            >
              Register
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
            <div>
              <input
                {...register('identifier')}
                type="text"
                className="w-full px-4 py-3 bg-[#2C2C2C] border border-transparent text-gray-300 placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-transparent rounded-xl outline-none transition-all"
                placeholder="Email / Phone number"
              />
              {errors.identifier && (
                <p className="mt-1.5 text-xs text-red-400">{errors.identifier.message}</p>
              )}
            </div>

            {activeTab === 'register' && ( // Only show Full Name for Register tab
              <div>
                <input
                  {...register('fullName')}
                  type="text"
                  className="w-full px-4 py-3 bg-[#2C2C2C] border border-transparent text-gray-300 placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-transparent rounded-xl outline-none transition-all"
                  placeholder="Full name"
                />
                {errors.fullName && (
                  <p className="mt-1.5 text-xs text-red-400">{errors.fullName.message}</p>
                )}
              </div>
            )}

            <div>
              <input
                {...register('pin')}
                type="password" // Using password type for masking
                maxLength={6}
                className="w-full px-4 py-3 bg-[#2C2C2C] border border-transparent text-gray-300 placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-transparent rounded-xl outline-none transition-all"
                placeholder="6 Digit Pin"
              />
              {errors.pin && <p className="mt-1.5 text-xs text-red-400">{errors.pin.message}</p>}
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:opacity-90 disabled:opacity-60 text-white font-semibold py-3 px-4 rounded-full flex items-center justify-center transition-opacity shadow-lg"
            >
              {isLoading ? (
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              ) : activeTab === 'login' ? (
                'Login to Your Account'
              ) : (
                'Create Account'
              )}
              {/* Arrow only for Login button as per UI */}
              {activeTab === 'login' && !isLoading && <FaArrowRight className="ml-auto text-lg" />}
            </button>
          </form>

          {/* Helper Links */}
          <div className="mt-5 flex justify-between items-center text-xs">
            <span className="text-gray-400">
              {activeTab === 'login' ? "Don't have an account yet?" : 'Already have an account?'}{' '}
              <button
                onClick={() => setActiveTab(activeTab === 'login' ? 'register' : 'login')}
                className="font-semibold text-orange-400 hover:text-orange-300"
              >
                {activeTab === 'login' ? 'Register now!' : 'Login now!'}
              </button>
            </span>
            {activeTab === 'login' && (
              <Link href="/forgot-password" className="text-gray-500 hover:text-gray-300">
                Forgot Password?
              </Link>
            )}
          </div>

          {/* Divider */}
          <div className="my-8 flex items-center">
            <hr className="flex-grow border-t border-gray-600" />
            <span className="mx-3 text-gray-500 text-xs">OR</span>
            <hr className="flex-grow border-t border-gray-600" />
          </div>

          {/* Social Login Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => handleSocialSignIn('facebook')}
              disabled={isLoading}
              className="w-full bg-[#2C2C2C] hover:bg-[#3A3A3A] disabled:opacity-60 text-gray-300 font-medium py-2.5 px-4 rounded-lg flex items-center justify-center transition-colors"
            >
              <FaFacebookF className="mr-3 text-[#1877F2]" size={18} />
              Sign in with Facebook
            </button>
            <button
              onClick={() => handleSocialSignIn('google')}
              disabled={isLoading}
              className="w-full bg-[#2C2C2C] hover:bg-[#3A3A3A] disabled:opacity-60 text-gray-300 font-medium py-2.5 px-4 rounded-lg flex items-center justify-center transition-colors"
            >
              <FaGoogle className="mr-3 text-[#DB4437]" size={18} />
              Sign in with Google
            </button>
          </div>
        </div>

        {/* Footer Links within Form Panel */}
        <div className="absolute bottom-6 sm:bottom-8 text-center w-full max-w-sm px-4">
          <div className="flex flex-col sm:flex-row justify-center items-center text-[11px] text-gray-500 space-y-1 sm:space-y-0 sm:space-x-3">
            <Link href="/terms" className="hover:text-gray-300">
              Terms & Conditions
            </Link>
            <span className="hidden sm:inline">|</span>
            <Link href="/privacy" className="hover:text-gray-300">
              Privacy Policy
            </Link>
          </div>
          <p className="mt-2 text-[11px] text-gray-600">
            © {new Date().getFullYear()} Typing.com.co. All rights reserved.
          </p>
        </div>
      </div>
      {/* CSS for dot pattern - add this to your global CSS or a style tag */}
      <style jsx global>{`
        .bg-dot-pattern-orange {
          background-image: radial-gradient(
            circle,
            rgba(255, 255, 255, 0.05) 1px,
            transparent 1px
          ); /* Lighter dots for orange bg */
          background-size: 15px 15px;
        }
      `}</style>
    </div>
  );
}

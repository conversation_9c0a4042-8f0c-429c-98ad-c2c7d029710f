{"name": "typing-com-co", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.0.1", "assert": "^2.1.0", "aws4": "^1.13.2", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-browserify": "^3.12.1", "framer-motion": "^12.15.0", "https-browserify": "^1.0.0", "lucide-react": "^0.518.0", "mongoose": "^8.15.0", "next": "^15.3.3", "next-auth": "^4.24.11", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwind-merge": "^2.6.0", "url": "^0.11.4", "util": "^0.12.5", "zod": "^3.24.4", "zustand": "^4.5.6"}, "devDependencies": {"@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20.17.30", "@types/react": "^18.3.20", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "9.27.0", "eslint-config-next": "^15.3.2", "jsdom": "^24.1.3", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "vitest": "^1.6.1"}}